#!/usr/bin/env python3
"""
Example usage of the /voice_list endpoint for dubbing video
"""
import requests
import json

def get_voice_list(tts_type, language=None):
    """
    Get voice list for a specific TTS service

    Args:
        tts_type (int): TTS service type (0-16)
        language (str, optional): Language filter (e.g., 'zh', 'en')

    Returns:
        dict: Voice list response
    """
    url = "http://127.0.0.1:9011/voice_list"

    params = {"tts_type": tts_type}
    if language:
        params["language"] = language

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"code": 1, "msg": f"Request error: {str(e)}"}

def main():
    """Example usage of the voice list endpoint"""
    
    print("Voice List API Example")
    print("=" * 50)
    
    # Example 1: Get all voices for Edge-TTS
    print("\n1. Getting all voices for Edge-TTS (tts_type=0):")
    result = get_voice_list(tts_type=0)
    if result.get("code") == 0:
        data = result["data"]
        print(f"   Service: {data['service_name']}")
        voices = data['voices']
        print(f"   Total voices available: {len(voices)}")

        # Show first 5 voices as sample
        sample_voices = voices[:5]
        print(f"   Sample voices: {sample_voices}")
    else:
        print(f"   Error: {result.get('msg')}")

    # Example 2: Get Chinese voices only for Edge-TTS
    print("\n2. Getting Chinese voices only for Edge-TTS:")
    result = get_voice_list(tts_type=0, language="zh")
    if result.get("code") == 0:
        data = result["data"]
        voices = data['voices']
        print(f"   Chinese voices found: {len(voices)}")
        print(f"   Sample: {voices[:3]}")
    else:
        print(f"   Error: {result.get('msg')}")

    # Example 3: Get voices for OpenAI TTS
    print("\n3. Getting voices for OpenAI TTS (tts_type=8):")
    result = get_voice_list(tts_type=8)
    if result.get("code") == 0:
        data = result["data"]
        print(f"   Service: {data['service_name']}")
        voices = data['voices']
        print(f"   Available voices: {voices}")
    else:
        print(f"   Error: {result.get('msg')}")

    # Example 4: Get voices for Azure TTS
    print("\n4. Getting voices for Azure TTS (tts_type=5):")
    result = get_voice_list(tts_type=5)
    if result.get("code") == 0:
        data = result["data"]
        print(f"   Service: {data['service_name']}")
        voices = data['voices']
        if voices:
            print(f"   Total voices: {len(voices)}")
            print(f"   Sample: {voices[:5]}")
        else:
            print("   No voices configured (may need Azure TTS setup)")
    else:
        print(f"   Error: {result.get('msg')}")
    
    # Example 5: Error handling - invalid tts_type
    print("\n5. Error handling example (invalid tts_type):")
    result = get_voice_list(tts_type=999)
    print(f"   Expected error: {result.get('msg')}")
    
    print("\n" + "=" * 50)
    print("TTS Service Types Reference:")
    print("0=Edge-TTS, 1=CosyVoice, 2=ChatTTS, 3=302.AI, 4=FishTTS")
    print("5=Azure-TTS, 6=GPT-SoVITS, 7=clone-voice, 8=OpenAI TTS")
    print("9=Elevenlabs.io, 10=Google TTS, 11=Custom TTS API")
    print("12=VolcEngine TTS, 13=F5-TTS, 14=kokoro-TTS")
    print("15=Google Cloud TTS, 16=Gemini TTS")

if __name__ == "__main__":
    main()
