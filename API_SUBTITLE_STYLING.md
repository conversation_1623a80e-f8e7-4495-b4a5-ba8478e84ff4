# Subtitle Styling and Dubbing API

This document describes the new `/v2/apply_subtitle_style` endpoint that allows you to apply custom styling and dubbing to videos using SRT subtitles.

## Endpoint

**POST** `/v2/apply_subtitle_style`

## Description

This endpoint processes an SRT subtitle file and a video file from provided URLs or local paths, applies custom styling to the subtitles, optionally generates dubbing audio, and merges everything into a final video with styled subtitles and/or dubbing.

## Request Format

**Content-Type:** `application/json`

### Required Parameters

- `srt_url` (string): URL or local path to the SRT subtitle file
- `video_url` (string): URL or local path to the video file

### Optional Parameters

#### Basic Settings
- `voiceSeparation` (boolean): Whether to perform voice separation (default: false)
- `sourceLanguage` (string): Source language code (default: "en")
- `targetLanguage` (string): Target language code (default: "zh-cn")
- `subtitleLayout` (string): Subtitle layout - "single" or "double" (default: "single")

#### subtitleStyle (object)
**Primary Subtitle Settings:**
- `fontSize` (integer): Primary subtitle font size (default: 15)
- `fontFamily` (string): Primary subtitle font family (default: "NotoSansCJK-Regular")
- `primaryColor` (string): Primary subtitle color in BGR format (default: "&H00A6A6E2")
- `primaryStrokeWidth` (integer): Primary subtitle stroke width (default: 1)
- `shadowColor` (string): Shadow color (default: "&H80000000")
- `showPrimaryShadow` (boolean): Show primary subtitle shadow (default: false)
- `showPrimaryStroke` (boolean): Show primary subtitle stroke (default: false)
- `primaryMarginV` (integer): Primary subtitle vertical margin (default: 0)
- `primaryBackgroundColor` (string): Primary subtitle background color (default: "&H33000000")
- `showPrimaryBackground` (boolean): Show primary subtitle background (default: false)

**Secondary Subtitle Settings (for double layout):**
- `secondaryFontSize` (integer): Secondary subtitle font size (default: 22)
- `secondaryFontFamily` (string): Secondary subtitle font family
- `secondaryColor` (string): Secondary subtitle color (default: "&H0028E2A1")
- `secondaryStrokeColor` (string): Secondary subtitle stroke color (default: "&H000505E1")
- `secondaryStrokeWidth` (integer): Secondary subtitle stroke width (default: 1)
- `secondaryBackgroundColor` (string): Secondary subtitle background color (default: "&H000F0F4D")
- `showSecondaryShadow` (boolean): Show secondary subtitle shadow (default: false)
- `showSecondaryStroke` (boolean): Show secondary subtitle stroke (default: false)
- `showSecondaryBackground` (boolean): Show secondary subtitle background (default: true)
- `secondaryMarginV` (integer): Secondary subtitle vertical margin (default: 32)

#### dubbing_settings (object)
If provided, dubbing will be generated. If omitted, only subtitle styling will be applied.

- `tts_type` (integer): TTS service type (default: 0)
  - 0 = Edge-TTS
  - 1 = CosyVoice
  - 2 = ChatTTS
  - 3 = 302.AI
  - 4 = FishTTS
  - 5 = Azure-TTS
  - 6 = GPT-SoVITS
  - 7 = clone-voice
  - 8 = OpenAI TTS
  - 9 = Elevenlabs.io
  - 10 = Google TTS
  - 11 = Custom TTS API
- `voice_role` (string): Voice role name (depends on TTS service)
- `voice_rate` (string): Speech rate, e.g., "+0%" for normal, "+50%" for faster (default: "+0%")
- `volume` (string): Volume adjustment, e.g., "+0%" for normal, "+20%" for louder (default: "+0%")
- `pitch` (string): Pitch adjustment, e.g., "+0Hz" for normal, "+100Hz" for higher (default: "+0Hz")
- `target_language` (string): Target language code, e.g., "zh-cn", "en", "fr" (default: "zh-cn")

## Response Format

### Success Response
```json
{
  "code": 0,
  "msg": "ok",
  "task_id": "unique_task_identifier"
}
```

### Error Response
```json
{
  "code": 1,
  "msg": "Error description"
}
```

## Task Status Monitoring

Use the existing `/task_status` endpoint to monitor progress:

**POST** `/task_status`
```json
{
  "task_id": "task_id_from_response"
}
```

### Status Response Codes
- `code: -1` - Task in progress, `msg` contains progress information
- `code: 0` - Task completed successfully, `data.url` contains download URLs
- `code: > 0` - Task failed, `msg` contains error information

## Example Usage

### Single Subtitle Layout (No Dubbing)
```python
import requests

response = requests.post("http://127.0.0.1:9011/v2/apply_subtitle_style", json={
    "srt_url": "http://127.0.0.1:9011/file/subtitles.srt",
    "video_url": "http://127.0.0.1:9011/file/video.mp4",
    "voiceSeparation": False,
    "sourceLanguage": "en",
    "targetLanguage": "zh-cn",
    "subtitleLayout": "single",
    "subtitleStyle": {
        "fontSize": 20,
        "fontFamily": "Arial",
        "primaryColor": "&H00FFFFFF",
        "primaryStrokeWidth": 2,
        "showPrimaryStroke": True,
        "primaryMarginV": 10
    }
})

print(response.json())
```

### Double Subtitle Layout with Dubbing
```python
import requests

response = requests.post("http://127.0.0.1:9011/v2/apply_subtitle_style", json={
    "srt_url": "http://127.0.0.1:9011/file/subtitles.srt",
    "video_url": "http://127.0.0.1:9011/file/video.mp4",
    "voiceSeparation": True,
    "sourceLanguage": "en",
    "targetLanguage": "vi",
    "subtitleLayout": "double",
    "subtitleStyle": {
        "fontSize": 15,
        "fontFamily": "NotoSansCJK-Regular",
        "primaryColor": "&H00A6A6E2",
        "primaryStrokeWidth": 1,
        "showPrimaryStroke": False,
        "primaryMarginV": 0,
        "primaryBackgroundColor": "&H33000000",
        "showPrimaryBackground": False,
        "secondaryFontSize": 22,
        "secondaryFontFamily": "NotoSansCJK-Regular",
        "secondaryColor": "&H0028E2A1",
        "secondaryStrokeColor": "&H000505E1",
        "secondaryStrokeWidth": 1,
        "secondaryBackgroundColor": "&H000F0F4D",
        "showSecondaryStroke": False,
        "showSecondaryBackground": True,
        "secondaryMarginV": 32
    },
    "dubbing_settings": {
        "tts_type": 0,
        "voice_role": "zh-CN-YunjianNeural",
        "voice_rate": "+0%",
        "volume": "+0%",
        "pitch": "+0Hz"
    }
})

task_id = response.json()["task_id"]

# Monitor progress
import time
while True:
    status = requests.post("http://127.0.0.1:9011/task_status", json={"task_id": task_id})
    result = status.json()
    
    if result["code"] == 0:
        print("Task completed!")
        print("Download URLs:", result["data"]["url"])
        break
    elif result["code"] > 0:
        print("Task failed:", result["msg"])
        break
    else:
        print("Progress:", result["msg"])
        time.sleep(5)
```

## Color Format

Colors use BGR (Blue-Green-Red) format with hexadecimal values:
- `&hffffff` - White
- `&h000000` - Black
- `&hff0000` - Blue
- `&h00ff00` - Green
- `&h0000ff` - Red
- `&hffff00` - Cyan
- `&hff00ff` - Magenta
- `&h00ffff` - Yellow

## Voice Roles

To get available voice roles for a specific TTS service, use the `/voices` endpoint:

```python
response = requests.get("http://127.0.0.1:9011/voices", params={
    "tts_type": 0,  # Edge-TTS
    "language": "zh"  # Optional language filter
})
voices = response.json()["data"]["voices"]
```

## Error Codes

- `code: 1` - General error (invalid parameters, file not found, etc.)
- `code: 4` - TTS language not supported
- `code: 5` - TTS API configuration error

## Notes

1. The endpoint downloads files from the provided URLs, so ensure they are accessible
2. Large video files may take significant time to process
3. The task runs asynchronously - use the task status endpoint to monitor progress
4. Generated files are available via the URLs returned in the task completion response
5. Hard subtitles are burned into the video, while soft subtitles can be toggled on/off by the player
