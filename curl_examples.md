# Voice List API - cURL Examples

This document provides cURL examples for testing the `/voice_list` endpoint.

## Basic Usage

### Get all voices for Edge-TTS (tts_type=0)
```bash
curl "http://127.0.0.1:9011/voice_list?tts_type=0"
```

### Get all voices for OpenAI TTS (tts_type=8)
```bash
curl "http://127.0.0.1:9011/voice_list?tts_type=8"
```

### Get all voices for Azure TTS (tts_type=5)
```bash
curl "http://127.0.0.1:9011/voice_list?tts_type=5"
```

## Language Filtering

### Get Chinese voices only from Edge-TTS
```bash
curl "http://127.0.0.1:9011/voice_list?tts_type=0&language=zh"
```

### Get English voices only from Edge-TTS
```bash
curl "http://127.0.0.1:9011/voice_list?tts_type=0&language=en"
```

### Get Japanese voices only from kokoro-TTS
```bash
curl "http://127.0.0.1:9011/voice_list?tts_type=14&language=ja"
```

## Error Testing

### Missing tts_type parameter
```bash
curl "http://127.0.0.1:9011/voice_list"
```

### Invalid tts_type (negative)
```bash
curl "http://127.0.0.1:9011/voice_list?tts_type=-1"
```

### Invalid tts_type (too large)
```bash
curl "http://127.0.0.1:9011/voice_list?tts_type=999"
```

## Pretty JSON Output

### Using jq for formatted output
```bash
curl "http://127.0.0.1:9011/voice_list?tts_type=0" | jq '.'
```

### Get only the voices array
```bash
curl "http://127.0.0.1:9011/voice_list?tts_type=0" | jq '.data.voices'
```

### Count voices
```bash
curl "http://127.0.0.1:9011/voice_list?tts_type=0" | jq '.data.voices | length'
```

### Get first 5 voices
```bash
curl "http://127.0.0.1:9011/voice_list?tts_type=0" | jq '.data.voices[:5]'
```

## All TTS Services

### Test all available TTS services
```bash
# Edge-TTS
curl "http://127.0.0.1:9011/voice_list?tts_type=0"

# CosyVoice
curl "http://127.0.0.1:9011/voice_list?tts_type=1"

# ChatTTS
curl "http://127.0.0.1:9011/voice_list?tts_type=2"

# 302.AI
curl "http://127.0.0.1:9011/voice_list?tts_type=3"

# FishTTS
curl "http://127.0.0.1:9011/voice_list?tts_type=4"

# Azure-TTS
curl "http://127.0.0.1:9011/voice_list?tts_type=5"

# GPT-SoVITS
curl "http://127.0.0.1:9011/voice_list?tts_type=6"

# clone-voice
curl "http://127.0.0.1:9011/voice_list?tts_type=7"

# OpenAI TTS
curl "http://127.0.0.1:9011/voice_list?tts_type=8"

# Elevenlabs.io
curl "http://127.0.0.1:9011/voice_list?tts_type=9"

# Google TTS
curl "http://127.0.0.1:9011/voice_list?tts_type=10"

# Custom TTS API
curl "http://127.0.0.1:9011/voice_list?tts_type=11"

# VolcEngine TTS
curl "http://127.0.0.1:9011/voice_list?tts_type=12"

# F5-TTS
curl "http://127.0.0.1:9011/voice_list?tts_type=13"

# kokoro-TTS
curl "http://127.0.0.1:9011/voice_list?tts_type=14"

# Google Cloud TTS
curl "http://127.0.0.1:9011/voice_list?tts_type=15"

# Gemini TTS
curl "http://127.0.0.1:9011/voice_list?tts_type=16"
```

## Response Format

All requests return JSON in this format:

```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "service_name": "Edge-TTS",
    "service_id": 0,
    "voices": [
      "zh-CN-XiaoxiaoNeural",
      "zh-CN-YunxiNeural",
      "en-US-AriaNeural"
    ]
  }
}
```

Error responses:
```json
{
  "code": 1,
  "msg": "Error message"
}
```
