#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for the new /file/<path:filepath> endpoint
"""

import requests
import json
import tempfile
import os
import time

def test_upload_and_file_access():
    """Test uploading a file and accessing it via the new /file/ endpoint"""
    
    # API endpoints
    upload_url = "http://127.0.0.1:9011/upload"
    
    # Create a temporary test file
    test_content = f"""This is a test file for the new /file/ endpoint.
Content: Hello World!
Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}
Random data: {os.urandom(16).hex()}
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
        temp_file.write(test_content)
        temp_file_path = temp_file.name
    
    try:
        print("🚀 Testing upload and file access...")
        print("=" * 50)
        
        # Step 1: Upload the file
        print("📤 Step 1: Uploading file...")
        with open(temp_file_path, 'rb') as f:
            files = {'file': f}
            upload_response = requests.post(upload_url, files=files)
        
        print(f"Upload Status Code: {upload_response.status_code}")
        
        if upload_response.status_code != 200:
            print("❌ Upload failed!")
            return False
        
        upload_data = upload_response.json()
        print(f"Upload Response: {json.dumps(upload_data, indent=2)}")
        
        if upload_data.get('code') != 0:
            print(f"❌ Upload failed: {upload_data.get('msg')}")
            return False
        
        print("✅ Upload successful!")
        
        # Step 2: Access the file via the new /file/ endpoint
        file_url = upload_data['data']['url']
        file_path = upload_data['data']['file_path']
        
        print(f"\n📥 Step 2: Accessing file via new endpoint...")
        print(f"File URL: {file_url}")
        print(f"File Path: {file_path}")
        
        access_response = requests.get(file_url)
        print(f"Access Status Code: {access_response.status_code}")
        
        if access_response.status_code == 200:
            print("✅ File access successful!")
            retrieved_content = access_response.text
            print(f"Retrieved content length: {len(retrieved_content)} bytes")
            
            # Verify content matches
            if retrieved_content.strip() == test_content.strip():
                print("✅ Content verification successful!")
                return True
            else:
                print("❌ Content verification failed!")
                print(f"Expected: {test_content[:100]}...")
                print(f"Got: {retrieved_content[:100]}...")
                return False
        else:
            print(f"❌ File access failed! Status: {access_response.status_code}")
            if access_response.headers.get('content-type', '').startswith('application/json'):
                try:
                    error_data = access_response.json()
                    print(f"Error response: {json.dumps(error_data, indent=2)}")
                except:
                    pass
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed! Make sure the API server is running on http://127.0.0.1:9011")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False
    finally:
        # Clean up temporary file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

def test_file_endpoint_security():
    """Test security features of the /file/ endpoint"""
    
    print("\n🔒 Testing security features...")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:9011/file"
    
    # Test cases for security
    security_tests = [
        ("../etc/passwd", "Directory traversal with ../"),
        ("../../etc/passwd", "Directory traversal with ../../"),
        ("/etc/passwd", "Absolute path"),
        ("../../../etc/passwd", "Multiple directory traversal"),
        ("subdir/../../../etc/passwd", "Mixed path with traversal"),
    ]
    
    for test_path, description in security_tests:
        try:
            print(f"\n🧪 Testing: {description}")
            print(f"Path: {test_path}")
            
            response = requests.get(f"{base_url}/{test_path}")
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 400:
                print("✅ Correctly blocked malicious path!")
            elif response.status_code == 403:
                print("✅ Correctly denied access!")
            elif response.status_code == 404:
                print("✅ File not found (expected for security test)")
            else:
                print(f"⚠️  Unexpected response: {response.status_code}")
                if response.headers.get('content-type', '').startswith('application/json'):
                    try:
                        error_data = response.json()
                        print(f"Response: {json.dumps(error_data, indent=2)}")
                    except:
                        pass
                        
        except Exception as e:
            print(f"❌ Security test failed with error: {str(e)}")

def test_file_endpoint_not_found():
    """Test /file/ endpoint with non-existent files"""
    
    print("\n🔍 Testing non-existent file access...")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:9011/file"
    
    # Test non-existent files
    test_cases = [
        "nonexistent.txt",
        "20241201/nonexistent.mp4",
        "subdir/missing.pdf",
    ]
    
    for test_path in test_cases:
        try:
            print(f"\n🧪 Testing non-existent file: {test_path}")
            
            response = requests.get(f"{base_url}/{test_path}")
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 404:
                print("✅ Correctly returned 404 for non-existent file!")
                if response.headers.get('content-type', '').startswith('application/json'):
                    try:
                        error_data = response.json()
                        print(f"Error response: {json.dumps(error_data, indent=2)}")
                    except:
                        pass
            else:
                print(f"⚠️  Unexpected response: {response.status_code}")
                        
        except Exception as e:
            print(f"❌ Test failed with error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Testing new /file/<path:filepath> endpoint...")
    print("=" * 60)
    
    # Run all tests
    success = test_upload_and_file_access()
    test_file_endpoint_security()
    test_file_endpoint_not_found()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Main functionality test PASSED!")
    else:
        print("❌ Main functionality test FAILED!")
    print("🏁 All tests completed!")
