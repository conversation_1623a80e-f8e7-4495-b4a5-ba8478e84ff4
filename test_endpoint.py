#!/usr/bin/env python3
"""
Test script to verify the /v1/chat/completions endpoint works correctly
"""

import requests
import json

def test_chat_completions():
    """Test the new chat completions endpoint"""
    
    # Test 1: Valid request with Gemini model
    print("Test 1: Valid request with Gemini model")
    response = requests.post(
        'http://127.0.0.1:9012/v1/chat/completions',
        headers={
            'Accept': 'application/json',
            'Authorization': 'Bearer test_api_key',
            'Content-Type': 'application/json'
        },
        json={
            "model": "gemini-1.5-pro",
            "messages": [{"role": "user", "content": "Who are you?"}]
        }
    )
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()
    
    # Test 2: Valid request with GPT model
    print("Test 2: Valid request with GPT model")
    response = requests.post(
        'http://127.0.0.1:9012/v1/chat/completions',
        headers={
            'Accept': 'application/json',
            'Authorization': 'Bearer test_api_key',
            'Content-Type': 'application/json'
        },
        json={
            "model": "gpt-4",
            "messages": [{"role": "user", "content": "Hello, how are you?"}]
        }
    )
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()
    
    # Test 3: Missing Authorization header
    print("Test 3: Missing Authorization header")
    response = requests.post(
        'http://127.0.0.1:9012/v1/chat/completions',
        headers={
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        json={
            "model": "gemini-1.5-pro",
            "messages": [{"role": "user", "content": "Who are you?"}]
        }
    )
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()
    
    # Test 4: Missing model parameter
    print("Test 4: Missing model parameter")
    response = requests.post(
        'http://127.0.0.1:9012/v1/chat/completions',
        headers={
            'Accept': 'application/json',
            'Authorization': 'Bearer test_api_key',
            'Content-Type': 'application/json'
        },
        json={
            "messages": [{"role": "user", "content": "Who are you?"}]
        }
    )
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()
    
    # Test 5: Missing messages parameter
    print("Test 5: Missing messages parameter")
    response = requests.post(
        'http://127.0.0.1:9012/v1/chat/completions',
        headers={
            'Accept': 'application/json',
            'Authorization': 'Bearer test_api_key',
            'Content-Type': 'application/json'
        },
        json={
            "model": "gemini-1.5-pro"
        }
    )
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()
    
    # Test 6: Stream parameter (should be rejected)
    print("Test 6: Stream parameter (should be rejected)")
    response = requests.post(
        'http://127.0.0.1:9012/v1/chat/completions',
        headers={
            'Accept': 'application/json',
            'Authorization': 'Bearer test_api_key',
            'Content-Type': 'application/json'
        },
        json={
            "model": "gemini-1.5-pro",
            "messages": [{"role": "user", "content": "Who are you?"}],
            "stream": True
        }
    )
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()
    
    # Test 7: Multi-turn conversation
    print("Test 7: Multi-turn conversation")
    response = requests.post(
        'http://127.0.0.1:9012/v1/chat/completions',
        headers={
            'Accept': 'application/json',
            'Authorization': 'Bearer test_api_key',
            'Content-Type': 'application/json'
        },
        json={
            "model": "gemini-1.5-pro",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "What is the capital of France?"},
                {"role": "assistant", "content": "The capital of France is Paris."},
                {"role": "user", "content": "What about Italy?"}
            ]
        }
    )
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

def test_server_health():
    """Test if the server is running"""
    try:
        response = requests.get('http://127.0.0.1:9012/health', timeout=5)
        print(f"Health check status: {response.status_code}")
        if response.status_code == 200:
            print("Server is running!")
            return True
        else:
            print("Server responded but with error status")
            return False
    except requests.exceptions.RequestException as e:
        print(f"Server is not running or not accessible: {e}")
        return False

if __name__ == '__main__':
    print("Testing /v1/chat/completions endpoint")
    print("=" * 50)
    
    # First check if server is running
    if test_server_health():
        print("\nRunning endpoint tests...")
        test_chat_completions()
    else:
        print("\nPlease start the API server first:")
        print("python api.py")
        print("\nOr run the test server:")
        print("python test_chat_api.py")
