#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for the /upload endpoint
"""

import requests
import json
import tempfile
import os

def test_upload_endpoint():
    """Test the /upload endpoint with a sample file"""
    
    # API endpoint
    api_url = "http://127.0.0.1:9011/upload"
    
    # Create a temporary test file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
        temp_file.write("This is a test file for upload endpoint testing.\n")
        temp_file.write("Content: Hello World!\n")
        temp_file.write("Timestamp: 2024-01-01 12:00:00\n")
        temp_file_path = temp_file.name
    
    try:
        # Test 1: Upload a file
        print("Testing file upload...")
        with open(temp_file_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(api_url, files=files)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                print("✅ Upload successful!")
                
                # Test 2: Try to access the uploaded file
                file_url = data['data']['url']
                print(f"\nTesting file access at: {file_url}")
                
                access_response = requests.get(file_url)
                print(f"Access Status Code: {access_response.status_code}")
                
                if access_response.status_code == 200:
                    print("✅ File access successful!")
                    print(f"File content: {access_response.text}")
                else:
                    print("❌ File access failed!")
            else:
                print(f"❌ Upload failed: {data.get('msg')}")
        else:
            print("❌ Upload request failed!")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed! Make sure the API server is running on http://127.0.0.1:9011")
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
    finally:
        # Clean up temporary file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

def test_upload_no_file():
    """Test the /upload endpoint without a file"""
    print("\nTesting upload without file...")
    
    api_url = "http://127.0.0.1:9011/upload"
    
    try:
        response = requests.post(api_url)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        data = response.json()
        if data.get('code') == 1 and 'No file provided' in data.get('msg', ''):
            print("✅ Correctly handled missing file!")
        else:
            print("❌ Did not handle missing file correctly!")
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")

def test_upload_empty_file():
    """Test the /upload endpoint with empty filename"""
    print("\nTesting upload with empty filename...")
    
    api_url = "http://127.0.0.1:9011/upload"
    
    try:
        # Create a file-like object with empty filename
        files = {'file': ('', b'test content', 'text/plain')}
        response = requests.post(api_url, files=files)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        data = response.json()
        if data.get('code') == 1 and 'No file selected' in data.get('msg', ''):
            print("✅ Correctly handled empty filename!")
        else:
            print("❌ Did not handle empty filename correctly!")
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Testing /upload endpoint...")
    print("=" * 50)
    
    test_upload_endpoint()
    test_upload_no_file()
    test_upload_empty_file()
    
    print("\n" + "=" * 50)
    print("✅ All tests completed!")
