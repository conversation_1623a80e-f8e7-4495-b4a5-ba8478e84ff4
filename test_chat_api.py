#!/usr/bin/env python3
"""
Minimal test script for the new /v1/chat/completions endpoint
"""

import json
import time
import uuid
from functools import wraps

from flask import Flask, request, jsonify
from flask_cors import CORS

# Mock config for testing
class MockConfig:
    def __init__(self):
        self.params = {
            'gemini_key': '',  # Set this to test Gemini
            'chatgpt_key': '',  # Set this to test OpenAI
            'chatgpt_api': 'https://api.openai.com/v1',
            'gemini_model': 'gemini-1.5-pro',
            'localllm_api': '',
            'localllm_key': '',
            'localllm_model': 'qwen:7b'
        }
        self.logger = self
    
    def info(self, msg):
        print(f"INFO: {msg}")
    
    def error(self, msg):
        print(f"ERROR: {msg}")

config = MockConfig()

app = Flask(__name__)
CORS(app)

# Authentication decorator for OpenAI-compatible endpoints
def require_auth(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            return jsonify({
                "error": {
                    "message": "You didn't provide an API key.",
                    "type": "invalid_request_error",
                    "param": None,
                    "code": None
                }
            }), 401
        
        if not auth_header.startswith('Bearer '):
            return jsonify({
                "error": {
                    "message": "Invalid authorization header format. Expected 'Bearer <token>'.",
                    "type": "invalid_request_error", 
                    "param": None,
                    "code": None
                }
            }), 401
        
        # Extract the token (everything after 'Bearer ')
        token = auth_header[7:]
        if not token:
            return jsonify({
                "error": {
                    "message": "You didn't provide an API key.",
                    "type": "invalid_request_error",
                    "param": None,
                    "code": None
                }
            }), 401
        
        # For testing, we'll accept any non-empty token
        return f(*args, **kwargs)
    return decorated_function

@app.route('/v1/chat/completions', methods=['POST'])
@require_auth
def chat_completions():
    try:
        data = request.json
        if not data:
            return jsonify({
                "error": {
                    "message": "Request body must be JSON",
                    "type": "invalid_request_error",
                    "param": None,
                    "code": None
                }
            }), 400
        
        # Validate required parameters
        model = data.get('model')
        messages = data.get('messages')
        
        if not model:
            return jsonify({
                "error": {
                    "message": "Missing required parameter: 'model'",
                    "type": "invalid_request_error",
                    "param": "model",
                    "code": None
                }
            }), 400
        
        if not messages:
            return jsonify({
                "error": {
                    "message": "Missing required parameter: 'messages'",
                    "type": "invalid_request_error",
                    "param": "messages",
                    "code": None
                }
            }), 400
        
        if not isinstance(messages, list) or len(messages) == 0:
            return jsonify({
                "error": {
                    "message": "'messages' must be a non-empty array",
                    "type": "invalid_request_error",
                    "param": "messages",
                    "code": None
                }
            }), 400
        
        # Extract optional parameters
        temperature = data.get('temperature', 0.7)
        max_tokens = data.get('max_tokens', 4096)
        top_p = data.get('top_p', 1.0)
        stream = data.get('stream', False)
        
        if stream:
            return jsonify({
                "error": {
                    "message": "Streaming is not currently supported",
                    "type": "invalid_request_error",
                    "param": "stream",
                    "code": None
                }
            }), 400
        
        # Generate response based on model
        response_content = _generate_chat_response(model, messages, temperature, max_tokens, top_p)
        
        # Create OpenAI-compatible response
        completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
        created_timestamp = int(time.time())
        
        return jsonify({
            "id": completion_id,
            "object": "chat.completion",
            "created": created_timestamp,
            "model": model,
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": response_content
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": _estimate_tokens(messages),
                "completion_tokens": _estimate_tokens([{"content": response_content}]),
                "total_tokens": _estimate_tokens(messages) + _estimate_tokens([{"content": response_content}])
            }
        })
        
    except Exception as e:
        config.logger.error(f"Chat completions error: {str(e)}")
        return jsonify({
            "error": {
                "message": f"Internal server error: {str(e)}",
                "type": "internal_server_error",
                "param": None,
                "code": None
            }
        }), 500

def _generate_chat_response(model, messages, temperature, max_tokens, top_p):
    """Generate chat response based on the specified model"""
    try:
        # Convert messages to text for processing
        conversation_text = ""
        for msg in messages:
            role = msg.get('role', 'user')
            content = msg.get('content', '')
            conversation_text += f"{role}: {content}\n"
        
        # Route to appropriate AI service based on model name
        if 'gemini' in model.lower():
            return _generate_gemini_response(conversation_text, model, temperature, max_tokens)
        elif 'gpt' in model.lower() or 'chatgpt' in model.lower():
            return _generate_openai_response(conversation_text, model, temperature, max_tokens, top_p)
        else:
            # Default fallback response for testing
            return f"Hello! I'm a test assistant using model '{model}'. You said: {conversation_text.split('user:')[-1].strip() if 'user:' in conversation_text else conversation_text}"
            
    except Exception as e:
        config.logger.error(f"Error generating chat response: {str(e)}")
        raise Exception(f"Failed to generate response: {str(e)}")

def _generate_gemini_response(conversation_text, model, temperature, max_tokens):
    """Generate response using Gemini API"""
    try:
        # Check if Gemini is configured
        api_key = config.params.get('gemini_key')
        if not api_key:
            return f"Gemini API key not configured. Test response for model '{model}': {conversation_text.split('user:')[-1].strip() if 'user:' in conversation_text else conversation_text}"
        
        # Try to use Gemini if available
        try:
            import google.generativeai as genai
            
            # Configure Gemini
            genai.configure(api_key=api_key)
            
            # Use the configured model or default
            gemini_model = config.params.get('gemini_model', 'gemini-1.5-pro')
            
            # Create model instance
            model_instance = genai.GenerativeModel(
                model_name=gemini_model,
                generation_config={
                    "max_output_tokens": max_tokens,
                    "temperature": temperature
                }
            )
            
            # Generate response
            response = model_instance.generate_content(conversation_text)
            
            if response and response.text:
                return response.text.strip()
            else:
                raise Exception("Empty response from Gemini")
                
        except ImportError:
            return f"Gemini library not available. Test response for model '{model}': {conversation_text.split('user:')[-1].strip() if 'user:' in conversation_text else conversation_text}"
            
    except Exception as e:
        config.logger.error(f"Gemini API error: {str(e)}")
        return f"Gemini error: {str(e)}. Test response for model '{model}': {conversation_text.split('user:')[-1].strip() if 'user:' in conversation_text else conversation_text}"

def _generate_openai_response(conversation_text, model, temperature, max_tokens, top_p):
    """Generate response using OpenAI API"""
    try:
        # Check if OpenAI is configured
        api_key = config.params.get('chatgpt_key')
        api_url = config.params.get('chatgpt_api', 'https://api.openai.com/v1')
        
        if not api_key:
            return f"OpenAI API key not configured. Test response for model '{model}': {conversation_text.split('user:')[-1].strip() if 'user:' in conversation_text else conversation_text}"
        
        try:
            from openai import OpenAI
            import httpx
            
            # Create OpenAI client
            client = OpenAI(
                api_key=api_key,
                base_url=api_url,
                http_client=httpx.Client(timeout=7200)
            )
            
            # Convert conversation text back to messages format
            messages = []
            lines = conversation_text.strip().split('\n')
            for line in lines:
                if ':' in line:
                    role, content = line.split(':', 1)
                    role = role.strip().lower()
                    content = content.strip()
                    if role in ['user', 'assistant', 'system']:
                        messages.append({"role": role, "content": content})
            
            # Generate response
            response = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p
            )
            
            if response.choices and response.choices[0].message:
                return response.choices[0].message.content.strip()
            else:
                raise Exception("Empty response from OpenAI")
                
        except ImportError:
            return f"OpenAI library not available. Test response for model '{model}': {conversation_text.split('user:')[-1].strip() if 'user:' in conversation_text else conversation_text}"
            
    except Exception as e:
        config.logger.error(f"OpenAI API error: {str(e)}")
        return f"OpenAI error: {str(e)}. Test response for model '{model}': {conversation_text.split('user:')[-1].strip() if 'user:' in conversation_text else conversation_text}"

def _estimate_tokens(messages):
    """Rough estimation of token count"""
    total_chars = 0
    for msg in messages:
        if isinstance(msg, dict):
            content = msg.get('content', '')
        else:
            content = str(msg)
        total_chars += len(content)
    # Rough approximation: 1 token ≈ 4 characters
    return max(1, total_chars // 4)

@app.route('/health', methods=['GET'])
def health():
    return jsonify({"status": "ok", "message": "Chat completions API is running"})

if __name__ == '__main__':
    print("Starting Chat Completions API test server...")
    print("Available endpoints:")
    print("  POST /v1/chat/completions - OpenAI-compatible chat completions")
    print("  GET  /health - Health check")
    print("\nServer running on http://127.0.0.1:9012")
    app.run(host='127.0.0.1', port=9012, debug=True)
